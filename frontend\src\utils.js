// TODO: move this file to utils folder

import dayjs from "dayjs";
import * as Colors from "@mui/material/colors";
import environment from "../environment";
import axiosInstance from "./axios";
import * as mgrs from "mgrs";
import LatLonSpherical from "geodesy/latlon-spherical.js";

var cacheLocations = {};

const CUTOFF_DATE = dayjs("06/01/2024").valueOf();

const datetimeFormats = {
    "MM/DD/YYYY h:mm:ss A": "North America",
    "DD/MM/YYYY h:mm:ss A": "Europe, Latin America, Australia/NZ, Africa, Asia",
    "YYYY-MM-DD HH:mm:ss": "East Asia, Canada",
};

const userValues = {
    dateTimeFormat: (user, { exclude_hours = false, exclude_minutes = false, exclude_seconds = false } = {}) => {
        const dateTimeFormat = user?.date_time_format || Object.keys(datetimeFormats)[0];

        const formatParts = dateTimeFormat.split(" ");

        if (formatParts.length < 2) {
            return dateTimeFormat;
        }

        if (exclude_hours) {
            return formatParts[0];
        }

        if (exclude_minutes) {
            const timeParts = formatParts[1].split(":");
            formatParts[1] = timeParts[0];
        }

        if (exclude_seconds) {
            const timeParts = formatParts[1].split(":");
            formatParts[1] = timeParts[0] + ":" + timeParts[1];
        }

        return formatParts.join(" ");
    },
};

const defaultValues = {
    zoom: 14, // zoom level on maps
    interval: 5, // coordinates interval on full map (in minutes)
    precision: 5, // coordinates precision on full map (in minutes)
    journeyStart: (() => dayjs(Date.now() - 86400000 * 3))(),
    journeyEnd: (() => dayjs(Date.now()))(),
    datapointsDistance: 3000, // in meters
    polylineColors: {
        "prototype-24": Colors.amber[700],
        "prototype-25": Colors.brown[700],
        "prototype-32": Colors.green[700],
        "prototype-33": Colors.blue[700],
        "prototype-36": Colors.deepPurple[700],
        "prototype-37": Colors.pink[700],
        "unit-3": Colors.teal[700],
        QSX0003: Colors.cyan[700],
        QSX0008: Colors.lime[700],
    },
    insetMapUpdateInterval: 1000, // in milliseconds
    dateTimeFormat: ({ exclude_hours = false, exclude_minutes = false, exclude_seconds = false } = {}) =>
        `DD-MMM-YYYY${exclude_hours ? "" : " HH"}${exclude_minutes ? "" : ":mm"}${exclude_seconds ? "" : ":ss"}`.trim(),
    timezone: "Asia/Shanghai",
    icons: {
        location:
            "M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7m0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5",
        image: "M21 19V5c0-1.1-.9-2-2-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2M8.5 13.5l2.5 3.01L14.5 12l4.5 6H5z",
        video: "M10 3H14V5H19C20.1 5 21 5.9 21 7V17C21 18.1 20.1 19 19 19H14V21H10V19H5C3.9 19 3 18.1 3 17V7C3 5.9 3.9 5 5 5H10V3M17 12L13 9V15L17 12Z",
    },
    polylineTypes: {
        DOTTED: 1,
        DASHED: 2,
        SOLID: 3,
    },
    homePortsFilterModes: {
        ALL: 1,
        ONLY_HOME_PORTS: 2,
        ONLY_NON_HOME_PORTS: 3,
    },
    eventsInterval: 86400000, // in ms
};

/** Assign the ID as per the permissions collection in the DB */
const permissions = {
    manageRoles: 100,
    manageUsers: 200,
    accessAllUnits: 300,
    viewSessionLogs: 400,
    manageApiKeys: 500,
    viewStatistics: 600,
    manageNotificationsAlerts: 700,
    manageRegionsGroups: 800,
    additionalEmailAddressesPrivilege: 900,
    manageOrganizations: 1000,
};

/** Assign the ID as per the roles collection in the DB */
const roles = {
    super_admin: 1,
    internal_admin: 16,
};

const daysOfWeek = ["Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"];
function getDayByIndex(dayIndex) {
    return daysOfWeek[dayIndex];
}

function seededRandom(seed) {
    let x = Math.sin(seed) * 10000;
    return x - Math.floor(x);
}

function generateRandomColorRGBA(seed, alpha = 1) {
    const red = Math.floor(seededRandom(seed) * 256);
    const green = Math.floor(seededRandom(seed + 1) * 256);
    const blue = Math.floor(seededRandom(seed + 2) * 256);

    return `rgba(${red}, ${green}, ${blue}, ${alpha})`;
}

function getDatesArrayBetweenDateRange(startDate, endDate) {
    console.log("getDatesArrayBetweenDateRange", startDate, endDate);
    return Array.from(
        { length: (new Date(endDate) - new Date(startDate)) / (1000 * 60 * 60 * 24) + 1 },
        (_, i) => new Date(new Date(startDate).setUTCDate(new Date(startDate).getUTCDate() + i)).toISOString().split("T")[0],
    );
}

function sortObject(obj) {
    // Convert the object into an array of key-value pairs
    const sortedArray = Object.entries(obj).sort(([, value1], [, value2]) => value2 - value1);

    // Convert the sorted array back into an object
    const sortedObj = Object.fromEntries(sortedArray);

    return sortedObj;
}

function isEnvironment(envs) {
    return envs.includes(environment.VITE_NODE_ENV);
}

function haversine(lat1, lng1, lat2, lng2) {
    const R = 6371; // Radius of Earth in kilometers
    const dLat = ((lat2 - lat1) * Math.PI) / 180;
    const dLng = ((lng2 - lng1) * Math.PI) / 180;
    const a =
        Math.sin(dLat / 2) * Math.sin(dLat / 2) +
        Math.cos((lat1 * Math.PI) / 180) * Math.cos((lat2 * Math.PI) / 180) * Math.sin(dLng / 2) * Math.sin(dLng / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c; // Distance in kilometers
}

async function getLocation({ lat, lng }) {
    const key = `${lat},${lng}`;
    return new Promise((resolve, reject) => {
        // Check if the location is within 30 km of any cached location
        for (const cachedKey in cacheLocations) {
            const [cachedLat, cachedLng] = cachedKey.split(",").map(Number);
            const distance = haversine(lat, lng, cachedLat, cachedLng);

            if (distance <= 5) {
                return resolve(cacheLocations[cachedKey]);
            }
        }

        // If not found in the cache, fetch from the API
        axiosInstance
            .get("/geolocations", { params: { lat, lng } })
            .then((res) => {
                const fetchedName = res.data.name;
                cacheLocations = { ...cacheLocations, [key]: fetchedName };
                resolve(fetchedName);
            })
            .catch((err) => {
                console.error("API call failed:", err);
                reject(err);
            });
    });
}

const displayCoordinates = (coordinates, showMGRS = false) => {
    if (!coordinates) return null;
    const [lng, lat] = coordinates;
    if (!showMGRS) {
        return coordinates.map((e) => e?.toFixed(8)).join(", ");
    } else {
        return mgrs.forward([lng, lat]);
    }
};

const handleDownload = (targetBlob, fileName = undefined) => {
    const downloadLink = document.createElement("a");
    downloadLink.href = URL.createObjectURL(targetBlob);

    if (fileName) {
        downloadLink.download = fileName;
    }

    // Programmatically "click" the link to start the download.
    downloadLink.click();
};

const procDownloadResponse = (response) => {
    const contentDisposition = response.headers["content-disposition"];
    let filename = "download.zip"; // Default filename

    if (contentDisposition) {
        const filenameRegex = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/;
        const matches = filenameRegex.exec(contentDisposition);
        if (matches != null && matches[1]) {
            filename = matches[1].replace(/['"]/g, ""); // Remove quotes
        }
    }

    handleDownload(response.data, filename);
};

//This is for validation the email
const validateEmailDomain = (email, user, emailDomains) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
        return "Invalid email format.";
    }
    const emailDomain = email.split("@")[1];
    const userDomain = user.email.split("@")[1];
    if (!user?.hasPermissions([permissions.additionalEmailAddressesPrivilege])) {
        if (emailDomain !== userDomain) {
            return "Email domain does not match your domain.";
        }
    }
    const isDomainAllowed = emailDomains.some((domain) => domain.domain === emailDomain);
    if (!isDomainAllowed) {
        return "Email domain is not allowed.";
    }
    return "";
};
function isDevUnit(vesselName) {
    if (!vesselName || typeof vesselName !== "string") return true;
    if (vesselName.toLowerCase().trim() === "unregistered") return true;
    return false;
}

const timezonesList = [
    { name: "GMT-12:00", offset: "-12:00", representative: "Etc/GMT-12" },
    { name: "GMT-11:00", offset: "-11:00", representative: "Pacific/Pago_Pago" },
    { name: "GMT-10:00", offset: "-10:00", representative: "Pacific/Honolulu" },
    { name: "GMT-09:00", offset: "-09:00", representative: "America/Anchorage" },
    { name: "GMT-08:00", offset: "-08:00", representative: "America/Los_Angeles" },
    { name: "GMT-07:00", offset: "-07:00", representative: "America/Denver" },
    { name: "GMT-06:00", offset: "-06:00", representative: "America/Costa_Rica" },
    { name: "GMT-05:00", offset: "-05:00", representative: "America/New_York" },
    { name: "GMT-04:00", offset: "-04:00", representative: "America/Santiago" },
    { name: "GMT-03:00", offset: "-03:00", representative: "America/Sao_Paulo" },
    { name: "GMT-02:00", offset: "-02:00", representative: "Atlantic/South_Georgia" },
    { name: "GMT-01:00", offset: "-01:00", representative: "Atlantic/Azores" },
    { name: "GMT+00:00", offset: "+00:00", representative: "UTC" },
    { name: "GMT+01:00", offset: "+01:00", representative: "Europe/Berlin" },
    { name: "GMT+02:00", offset: "+02:00", representative: "Africa/Cairo" },
    { name: "GMT+03:00", offset: "+03:00", representative: "Europe/Moscow" },
    { name: "GMT+04:00", offset: "+04:00", representative: "Asia/Dubai" },
    { name: "GMT+05:00", offset: "+05:00", representative: "Asia/Karachi" },
    { name: "GMT+05:30", offset: "+05:30", representative: "Asia/Kolkata" },
    { name: "GMT+06:00", offset: "+06:00", representative: "Asia/Dhaka" },
    { name: "GMT+07:00", offset: "+07:00", representative: "Asia/Bangkok" },
    { name: "GMT+08:00", offset: "+08:00", representative: "Asia/Shanghai" },
    { name: "GMT+09:00", offset: "+09:00", representative: "Asia/Tokyo" },
    { name: "GMT+10:00", offset: "+10:00", representative: "Australia/Sydney" },
    { name: "GMT+11:00", offset: "+11:00", representative: "Pacific/Noumea" },
    { name: "GMT+12:00", offset: "+12:00", representative: "Pacific/Auckland" },
];

const arraysSame = (a, b) => a.length === b.length && a.every((element, index) => element === b[index]);

const formatTime = (user, { timeInSeconds, totalDuration, referenceTime }) => {
    const remainingTimeInSeconds = totalDuration - timeInSeconds;
    const days = Math.floor(remainingTimeInSeconds / 86400);
    const hours = Math.floor((remainingTimeInSeconds % 86400) / 3600);
    const minutes = Math.floor((remainingTimeInSeconds % 3600) / 60);
    const seconds = Math.floor(remainingTimeInSeconds % 60);

    const startDate = dayjs(referenceTime.current - totalDuration * 1000);
    const pastDate = startDate.add(timeInSeconds, "second");
    const formattedDate = pastDate.format(userValues.dateTimeFormat(user));
    const timeString =
        days > 0
            ? `- ${days}D ${hours.toString().padStart(2, "0")}H ${minutes.toString().padStart(2, "0")}M ${seconds.toString().padStart(2, "0")}S`
            : `- ${hours.toString().padStart(2, "0")}H ${minutes.toString().padStart(2, "0")}M ${seconds.toString().padStart(2, "0")}S`;
    return { timeString, formattedDate };
};

const calculateDistance = (point1, point2) => {
    const p1 = new LatLonSpherical(point1.lat, point1.lng);
    const p2 = new LatLonSpherical(point2.lat, point2.lng);
    return p1.distanceTo(p2);
};

export {
    defaultValues,
    permissions,
    getDayByIndex,
    generateRandomColorRGBA,
    getDatesArrayBetweenDateRange,
    sortObject,
    roles,
    isEnvironment,
    getLocation,
    CUTOFF_DATE,
    haversine,
    cacheLocations,
    displayCoordinates,
    handleDownload,
    validateEmailDomain,
    isDevUnit,
    timezonesList,
    arraysSame,
    procDownloadResponse,
    formatTime,
    datetimeFormats,
    userValues,
    calculateDistance,
};
