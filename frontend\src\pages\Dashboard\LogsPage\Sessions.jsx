import {
    alpha,
    Grid,
    Avatar,
    CircularProgress,
    IconButton,
    Typography,
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableRow,
    Collapse,
} from "@mui/material";
import React, { useEffect, useRef, useState } from "react";
import axiosInstance from "../../../axios";
import dayjs from "dayjs";
import { getSocket } from "../../../socket";
import { useApp } from "../../../hooks/AppHook";
import FilterLogModal from "./FilterLogModal";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import ExpandLessIcon from "@mui/icons-material/ExpandLess";
import { North, SentimentVeryDissatisfied, South } from "@mui/icons-material";
import SwapVertIcon from "@mui/icons-material/SwapVert";
import { FixedSizeList as List } from "react-window";
import { useToaster } from "../../../hooks/ToasterHook";
import theme from "../../../theme";
import RenderRow from "./RenderRow";
import MobileRenderRow from "./MobileRenderRow";
import CustomFooter from "./CustomFooter";
import { useUser } from "../../../hooks/UserHook.jsx";
import { userValues } from "../../../utils.js";

const columns = [
    { field: "user.name", headerName: "Name" },
    { field: "connect_timestamp", headerName: "Last Connected" },
    { field: "disconnect_timestamp", headerName: "Disconnected" },
    { field: "device", headerName: "Device" },
    { field: "browser", headerName: "Browser" },
];

export default function Sessions({ showFilterModal, setShowFilterModal, searchQuery }) {
    const { isMobile, screenSize } = useApp();
    const { user } = useUser();
    const toaster = useToaster();

    const [filters, setFilters] = useState({
        status: null,
        full_name_or_browser_or_device: null,
        created_after: null,
    });

    const [logs, setLogs] = useState([]);
    const [isLoading, setIsLoading] = useState(true);
    const [page, setPage] = useState(1);
    const [rowsPerPage, setRowsPerPage] = useState(10);
    const [totalPages, setTotalPages] = useState(1);
    const [totalCount, setTotalCount] = useState(0);
    const [expandedGroups, setExpandedGroups] = useState({});
    const [sorting, setSorting] = useState({});
    const listRef = useRef();
    const [expandedRowData, setExpandedRowData] = useState({});
    const [loading, setLoading] = useState({});

    useEffect(() => {
        fetchLogs();

        const socket = getSocket();
        socket.on("logs/changed", handleLogsChanged);

        return () => {
            socket.off("logs/changed", handleLogsChanged);
        };
    }, [user]);

    useEffect(() => {
        fetchLogs();
    }, [sorting]);

    const handleLogsChanged = () => {
        fetchLogs();
    };

    const fetchLogs = async (dropPagination) => {
        try {
            setIsLoading(true);
            setExpandedGroups({});
            setExpandedRowData({});
            if (dropPagination) setPage(1);

            const sortingStr = Object.entries(sorting)
                .map(([key, value]) => `sorting[${key}]=${value}`)
                .join("&");

            const queryParams = new URLSearchParams({
                ...Object.fromEntries(Object.entries({ ...filters, ...searchQuery }).filter(([, value]) => value !== null && value !== "")),
                page: dropPagination ? 1 : page,
                rowsPerPage,
            });

            const { data } = await axiosInstance.get(`/logs/sessions?${queryParams.toString()}${sortingStr.length > 0 ? `&${sortingStr}` : ""}`);
            setLogs(Array.isArray(data.logs) && data.logs.length > 0 ? data.logs : []);
            setTotalPages(data.totalPages);
            setTotalCount(data.totalCount);
            if (!Array.isArray(data.logs) || data.logs.length === 0) {
                toaster("No data found for logs", { variant: "warning" });
            }
            setExpandedRowData({});
        } catch (err) {
            setLogs([]);
            toaster("Something went wrong", { variant: "error" });
            console.error("An error occurred while fetching logs on the Sessions Page", err);
        } finally {
            setIsLoading(false);
        }
    };

    const fetchUserLogs = async (userId, logId) => {
        try {
            setLoading((prev) => ({ ...prev, [userId]: true }));

            const queryParams = new URLSearchParams({
                ...Object.fromEntries(Object.entries({ ...filters, ...searchQuery, sorting }).filter(([, value]) => value !== null && value !== "")),
                userId,
            });

            const { data } = await axiosInstance.get(`/logs/sessions/user?${queryParams.toString()}`);
            if (Array.isArray(data) && data.length > 0) {
                setExpandedRowData((prev) => ({ ...prev, [userId]: data.filter((log) => log._id !== logId) }));
            }
        } catch (err) {
            toaster("Something went wrong while fetching user logs", { variant: "error" });
            console.error("An error occurred while fetching user logs on the Sessions Page", err);
        } finally {
            setLoading((prev) => ({ ...prev, [userId]: false }));
        }
    };

    const handleExpandClick = (username, userId, logId) => {
        setExpandedGroups((prev) => {
            const isExpanded = !prev[username];
            if (isExpanded && !expandedRowData[userId]) {
                fetchUserLogs(userId, logId);
            }
            return { ...prev, [username]: isExpanded };
        });
    };

    const handleSortClick = (field) => {
        // setSorting((prev) => {
        //     const currentDirection = prev[field] || "NONE";
        //     const nextDirection = currentDirection === "ASC" ? "DESC" : currentDirection === "DESC" ? "NONE" : "ASC";
        //     return { ...prev, [field]: nextDirection };
        // });

        setSorting((prev) => {
            const currentDirection = prev[field] || "NONE";
            const nextDirection = currentDirection === "ASC" ? "DESC" : currentDirection === "DESC" ? null : "ASC";

            if (nextDirection === null) {
                return {};
            }

            return { [field]: nextDirection };
        });
    };

    const handlePageChange = (event, newPage) => {
        setPage(newPage);
    };

    const handlePageSizeChange = (event) => {
        setRowsPerPage(event.target.value);
        setPage(1);
    };

    const noRowsOverlay = () => (
        <>
            <SentimentVeryDissatisfied sx={{ fontSize: "100px", color: theme.palette.custom.borderColor }} />
            <Typography variant="h6" component="div" gutterBottom color={theme.palette.custom.borderColor}>
                No data available
            </Typography>
        </>
    );

    const renderList = (user, username, userId, isMobile, RenderRow) => {
        const itemSize = !isMobile ? 60 : screenSize.xs ? 250 : 130;
        const maxHeight = 400;
        const userLogs =
            expandedRowData[userId]?.sort((a, b) => new Date(b.connect_timestamp).getTime() - new Date(a.connect_timestamp).getTime()) || [];

        if (!userLogs || userLogs.length === 0 || !expandedGroups[username] || !expandedRowData[userId]) {
            return null;
        }
        const itemCount = userLogs.length;
        const dynamicHeight = Math.min(maxHeight, itemCount * (itemSize + 10));

        return expandedGroups[username] ? (
            <List
                ref={listRef}
                height={dynamicHeight}
                itemCount={itemCount}
                itemSize={itemSize}
                width="100%"
                itemData={{ user, username, logs: userLogs }}
            >
                {RenderRow}
            </List>
        ) : null;
    };

    useEffect(() => {
        const maxPage = totalPages || Math.ceil(totalCount / rowsPerPage);
        if (page > maxPage && maxPage > 0) {
            setPage(1);
        }
    }, [page, rowsPerPage, logs]);

    useEffect(() => {
        fetchLogs(true);
    }, [rowsPerPage, filters, searchQuery]);

    useEffect(() => {
        fetchLogs(false);
    }, [page]);

    return (
        <Grid container color={"#FFFFFF"} flexDirection={"column"} height={"100%"}>
            <Grid
                container
                direction="column"
                overflow={"auto"}
                border={`1px solid ${theme.palette.custom.borderColor}`}
                borderRadius={"10px"}
                padding={"10px 15px"}
                size="grow"
            >
                <Grid container paddingY={1}>
                    {!isMobile &&
                        columns.map((col, idx) => (
                            <Grid
                                key={idx}
                                sx={{
                                    display: "flex",
                                    // justifyContent: "space-between",
                                    alignItems: "center",
                                    flex: 1,
                                    padding: 0,
                                    border: "none",
                                    paddingRight: 2,
                                }}
                            >
                                <IconButton width={"auto"} sx={{ padding: 0 }} onClick={() => handleSortClick(col.field)}>
                                    {sorting[col.field] === "ASC" ? (
                                        <North
                                            sx={{
                                                fontSize: "18px",
                                                color: "#FFFFFF",
                                                transition: "all 0.3s",
                                                "&:hover": { color: theme.palette.custom.mainBlue },
                                            }}
                                        />
                                    ) : sorting[col.field] === "DESC" ? (
                                        <South
                                            sx={{
                                                fontSize: "18px",
                                                color: "#FFFFFF",
                                                transition: "all 0.3s",
                                                "&:hover": { color: theme.palette.custom.mainBlue },
                                            }}
                                        />
                                    ) : (
                                        <SwapVertIcon
                                            sx={{
                                                fontSize: "18px",
                                                color: alpha("#FFFFFF", 0.5),
                                            }}
                                        />
                                    )}
                                </IconButton>
                                <Typography sx={{ color: theme.palette.custom.mainBlue }}>{col.headerName}</Typography>
                            </Grid>
                        ))}
                    {isMobile && (
                        <>
                            {["Name", "Details"].map((col, idx) => (
                                <Grid
                                    key={idx}
                                    sx={{
                                        color: theme.palette.custom.mainBlue,
                                        flex: col === "Name" ? 1 : 0,
                                        minWidth: col === "Details" ? "auto" : 0,
                                        padding: 0,
                                        border: "none",
                                    }}
                                >
                                    {col}
                                </Grid>
                            ))}
                        </>
                    )}
                </Grid>
                <Grid container overflow={"auto"} marginBottom={2} size="grow">
                    {isLoading && (
                        <Grid height="100%" display={"flex"} justifyContent={"center"} alignItems={"center"} size="grow">
                            <CircularProgress size={40} />
                        </Grid>
                    )}
                    {!isLoading && logs.length === 0 && (
                        <Grid
                            height={"100%"}
                            width={"100%"}
                            display={"flex"}
                            flexDirection={"column"}
                            justifyContent={"center"}
                            alignItems={"center"}
                            size="grow"
                        >
                            {noRowsOverlay()}
                        </Grid>
                    )}
                    {!isLoading && logs.length > 0 && (
                        <TableContainer>
                            <Table sx={{ minWidth: isMobile ? 0 : 650 }} aria-labelledby="tableTitle">
                                <TableBody>
                                    {!isMobile &&
                                        logs.map((log, idx) => (
                                            <React.Fragment key={idx}>
                                                <TableRow hover>
                                                    <TableCell
                                                        colSpan={5}
                                                        sx={{
                                                            paddingX: "0 !important",
                                                            borderBottom: `1px solid ${theme.palette.custom.borderColor}`,
                                                            opacity: loading[log.user_id] ? 0.5 : 1,
                                                            pointerEvents: loading[log.user_id] ? "none" : "auto",
                                                        }}
                                                    >
                                                        <Grid container display={"flex"}>
                                                            <Grid
                                                                container
                                                                display={"flex"}
                                                                flex={1}
                                                                alignItems={"center"}
                                                                justifyContent={"space-between"}
                                                                onClick={() =>
                                                                    !loading[log.user_id] &&
                                                                    handleExpandClick(log.user?.username, log.user_id, log._id)
                                                                }
                                                                sx={{
                                                                    cursor: loading[log.user_id] ? "not-allowed" : "pointer",
                                                                }}
                                                            >
                                                                <Grid gap={2} display={"flex"} alignItems={"center"}>
                                                                    <Avatar sx={{ width: "35px", height: "35px" }} />
                                                                    <Typography variant="h6" fontSize={"16px !important"} color={"#fff"}>
                                                                        {log.user?.name ?? log.user?.username}
                                                                    </Typography>
                                                                </Grid>
                                                                <Grid>
                                                                    <IconButton sx={{ padding: 0 }}>
                                                                        {loading[log.user_id] ? (
                                                                            <CircularProgress
                                                                                size={20}
                                                                                sx={{ color: alpha("#FFFFFF", 0.6), marginRight: 2 }}
                                                                            />
                                                                        ) : expandedGroups[log.user?.username] ? (
                                                                            <ExpandLessIcon
                                                                                sx={{
                                                                                    color: alpha(theme.palette.common.white, 0.6),
                                                                                    padding: 0,
                                                                                    marginRight: 2,
                                                                                }}
                                                                            />
                                                                        ) : (
                                                                            <ExpandMoreIcon
                                                                                sx={{
                                                                                    color: alpha(theme.palette.common.white, 0.6),
                                                                                    padding: 0,
                                                                                    marginRight: 2,
                                                                                }}
                                                                            />
                                                                        )}
                                                                    </IconButton>
                                                                </Grid>
                                                            </Grid>
                                                            {["connect_timestamp", "disconnect_timestamp", "device", "browser"].map((key, index) => {
                                                                return (
                                                                    <Grid key={index} flex={1} display={"flex"} alignItems={"center"}>
                                                                        <Typography variant="h6" fontSize={"16px !important"} color={"#fff"}>
                                                                            {key.includes("connect_timestamp")
                                                                                ? log[key]
                                                                                    ? dayjs(log[key]).format(
                                                                                          userValues.dateTimeFormat(user, { exclude_seconds: true }),
                                                                                      )
                                                                                    : "--"
                                                                                : (log[key] ?? "--")}
                                                                        </Typography>
                                                                    </Grid>
                                                                );
                                                            })}
                                                        </Grid>
                                                    </TableCell>
                                                </TableRow>
                                                <TableRow>
                                                    <TableCell
                                                        colSpan={5}
                                                        sx={{ padding: 0, borderBottom: `1px solid ${theme.palette.custom.borderColor}` }}
                                                    >
                                                        <Collapse
                                                            in={expandedGroups[log.user?.username]}
                                                            sx={{
                                                                width: "100%",
                                                                backgroundColor: alpha(theme.palette.custom.offline, 0.08),
                                                                "& .MuiCollapse-wrapperInner": {
                                                                    display: "flex",
                                                                    flexDirection: "column",
                                                                },
                                                            }}
                                                        >
                                                            {renderList(user, log.user?.username, log.user_id, false, RenderRow)}
                                                        </Collapse>
                                                    </TableCell>
                                                </TableRow>
                                            </React.Fragment>
                                        ))}
                                    {isMobile &&
                                        logs.map((log, idx) => (
                                            <React.Fragment key={idx}>
                                                <TableRow hover>
                                                    <TableCell colSpan={5} sx={{ paddingX: "0 !important", borderBottom: 0 }}>
                                                        <Grid container display={"flex"}>
                                                            <Grid
                                                                container
                                                                display={"flex"}
                                                                flex={1}
                                                                alignItems={"center"}
                                                                justifyContent={"space-between"}
                                                                onClick={() =>
                                                                    !loading[log.user_id] && handleExpandClick(log.user?.username, log.user_id)
                                                                }
                                                                style={{
                                                                    cursor: loading[log.user_id] ? "not-allowed" : "pointer",
                                                                    opacity: loading[log.user_id] ? 0.7 : 1,
                                                                    pointerEvents: loading[log.user_id] ? "none" : "auto",
                                                                }}
                                                            >
                                                                <Grid gap={2} display={"flex"} alignItems={"center"}>
                                                                    <Avatar sx={{ width: "35px", height: "35px" }} />
                                                                    <Typography variant="h6" fontSize={"16px !important"} color={"#fff"}>
                                                                        {log.user?.name ?? log.user?.username}
                                                                    </Typography>
                                                                </Grid>
                                                                <Grid>
                                                                    <IconButton sx={{ padding: 0 }}>
                                                                        {loading[log.user_id] ? (
                                                                            <CircularProgress
                                                                                size={20}
                                                                                sx={{ color: alpha("#FFFFFF", 0.6), marginRight: 2 }}
                                                                            />
                                                                        ) : expandedGroups[log.user?.username] ? (
                                                                            <ExpandLessIcon
                                                                                sx={{
                                                                                    color: alpha(theme.palette.common.white, 0.6),
                                                                                    padding: 0,
                                                                                    marginRight: 2,
                                                                                }}
                                                                            />
                                                                        ) : (
                                                                            <ExpandMoreIcon
                                                                                sx={{
                                                                                    color: alpha(theme.palette.common.white, 0.6),
                                                                                    padding: 0,
                                                                                    marginRight: 2,
                                                                                }}
                                                                            />
                                                                        )}
                                                                    </IconButton>
                                                                </Grid>
                                                            </Grid>
                                                        </Grid>
                                                    </TableCell>
                                                </TableRow>
                                                <TableRow>
                                                    <TableCell colSpan={5} sx={{ padding: 0, borderBottom: 0 }}>
                                                        <Collapse
                                                            in={expandedGroups[log.user?.username]}
                                                            sx={{
                                                                width: "100%",
                                                                backgroundColor: alpha(theme.palette.custom.offline, 0.08),
                                                                borderRadius: "10px",
                                                                padding: "0 20px",
                                                                "& .MuiCollapse-wrapperInner": {
                                                                    display: "flex",
                                                                    flexDirection: "column",
                                                                },
                                                            }}
                                                        >
                                                            {renderList(user, log.user?.username, log.user_id, true, MobileRenderRow)}
                                                        </Collapse>
                                                    </TableCell>
                                                </TableRow>
                                            </React.Fragment>
                                        ))}
                                </TableBody>
                            </Table>
                        </TableContainer>
                    )}
                </Grid>
                <CustomFooter
                    page={page}
                    rowsPerPage={rowsPerPage}
                    totalRows={totalCount || logs.length}
                    onPageChange={handlePageChange}
                    onRowsPerPageChange={handlePageSizeChange}
                />
            </Grid>
            <FilterLogModal showFilterModal={showFilterModal} setShowFilterModal={setShowFilterModal} setFilters={setFilters} />
        </Grid>
    );
}
