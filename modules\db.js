const mongoose = require('mongoose')

mongoose.set('strictQuery', false)

const db = {
    qm: mongoose.createConnection(process.env.MONGO_URI, { dbName: 'quartermaster' }),
    qmai: mongoose.createConnection(process.env.MONGO_URI, { dbName: 'artifact_processor' }),
}

db.qm.on('open', () => console.log('DB connected to Quartermaster'))
db.qmai.on('open', () => console.log('DB connected to QMAI'))

db.qm.on('error', (err) => console.error(err))
db.qmai.on('error', (err) => console.error(err))

module.exports = db