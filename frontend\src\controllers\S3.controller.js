import environment from "../../environment.js";

class S3Controller {
    // async fetchSignedUrl(artifact, cachedSrc) {
    //     let img = "";
    //     const key = artifact.video_path ? artifact.video_path : artifact.image_path;
    //     console.log("Cached Call", cachedSrc);
    //     // if (cachedSrc.current[key]) return cachedSrc.current[key];
    //     await axiosInstance
    //         .get("/S3/signedUrl", {
    //             params: {
    //                 key,
    //                 bucket_name: artifact.bucket_name,
    //                 region: artifact.aws_region,
    //             },
    //         })
    //         .then((response) => {
    //             img = response.data.signedUrl;
    //             // cachedSrc.current[key] = img;
    //         });
    //
    //     return img;
    // }

    fetchUrl(artifact, linkType = undefined) {
        return `${environment.VITE_API_URL}/api/artifacts${linkType ? `/${linkType}` : ""}/link/${artifact._id}${linkType === "video" ? `.mp4` : ""}`;
    }

    fetchPreviewUrl(artifact) {
        return this.fetchUrl(artifact, "thumbnail_image");
    }
}
const s3Controller = new S3Controller();

export default s3Controller;
